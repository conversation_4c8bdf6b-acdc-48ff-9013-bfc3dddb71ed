import { NextRequest, NextResponse } from "next/server";
import { sendBookingConfirmationEmail } from "@/lib/email-service";

export async function GET(request: NextRequest) {
	console.log("=== SIMPLE EMAIL TEST START ===");

	try {
		const result = await sendBookingConfirmationEmail(
			"<EMAIL>", // Your email
			"<PERSON> Viranin",
			{
				reservationNumber: "RES-MDGLR7V4-293YIL",
				serviceName: "Location de bateau - 70cv (avec permis)",
				date: "lundi 28 juillet 2025",
				time: "09:00",
				participants: 7,
				totalAmount: 380,
				specialRequests: "Email resend test",
			}
		);

		console.log("Simple email test result:", result);
		console.log("=== SIMPLE EMAIL TEST END ===");

		return NextResponse.json({
			success: true,
			emailResult: result,
			message: result.success ? "Email sent successfully!" : "<PERSON><PERSON> failed to send",
		});
	} catch (error) {
		console.error("Simple email test error:", error);
		console.log("=== SIMPLE EMAIL TEST END (ERROR) ===");

		return NextResponse.json(
			{
				success: false,
				error: error instanceof Error ? error.message : "Unknown error",
				message: "Email test failed",
			},
			{ status: 500 }
		);
	}
}
