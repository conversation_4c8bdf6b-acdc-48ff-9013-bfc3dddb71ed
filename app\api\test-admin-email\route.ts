import { NextRequest, NextResponse } from "next/server";
import { getAdminNotificationEmail } from "@/lib/admin-notifications";

export async function GET(request: NextRequest) {
	try {
		console.log("Testing admin notification email function...");

		const adminEmail = await getAdminNotificationEmail();
		
		return NextResponse.json({
			success: true,
			adminEmail,
			message: "Admin notification email retrieved successfully"
		});

	} catch (error) {
		console.error("Error testing admin email:", error);
		return NextResponse.json({
			success: false,
			error: error instanceof Error ? error.message : "Unknown error"
		}, { status: 500 });
	}
}
