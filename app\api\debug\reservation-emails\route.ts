import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { 
  sendBookingConfirmationEmail, 
  sendPaymentConfirmationEmail,
  sendInvoiceEmail 
} from "@/lib/email-service";
import { generateInvoicePDF, createInvoiceData } from "@/lib/invoice-generator";
import { generateBookingConfirmationPDF } from "@/lib/pdf-generator";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const reservationNumber = searchParams.get("reservationNumber");
    const action = searchParams.get("action"); // 'check' or 'resend'

    if (!reservationNumber) {
      return NextResponse.json({ error: "reservationNumber parameter is required" }, { status: 400 });
    }

    console.log(`=== RESERVATION EMAIL DEBUG: ${reservationNumber} ===`);

    // Get reservation details
    const { data: reservation, error: reservationError } = await supabase
      .from("reservations")
      .select(`
        *,
        customer:customers(first_name, last_name, email),
        service:services(name),
        payment:payments(*)
      `)
      .eq("reservation_number", reservationNumber)
      .single();

    if (reservationError || !reservation) {
      console.log("Reservation not found:", reservationError);
      return NextResponse.json({ 
        error: "Reservation not found",
        reservationNumber,
        details: reservationError?.message 
      }, { status: 404 });
    }

    console.log("Found reservation:", {
      id: reservation.id,
      status: reservation.status,
      customer_email: reservation.customer?.email,
      service_name: reservation.service?.name,
      payment_count: reservation.payment?.length || 0
    });

    const customerName = `${reservation.customer.first_name} ${reservation.customer.last_name}`;
    const serviceName = reservation.service.name;
    
    // Format date and time
    const startTime = new Date(reservation.start_time);
    const date = startTime.toLocaleDateString("fr-FR");
    const time = startTime.toLocaleTimeString("fr-FR", {
      hour: "2-digit",
      minute: "2-digit",
    });

    if (action === "resend") {
      console.log("=== RESENDING EMAILS ===");
      const results = [];

      try {
        // 1. Send booking confirmation email
        console.log("Generating booking confirmation PDF...");
        const pdfData = await generateBookingConfirmationPDF({
          reservationNumber: reservation.reservation_number,
          customerName,
          serviceName,
          date,
          time,
          participants: reservation.participant_count,
          totalAmount: reservation.total_amount,
          specialRequests: reservation.special_requests,
        });

        const pdfBuffer = Buffer.from(await pdfData.arrayBuffer());

        console.log("Sending booking confirmation email...");
        const bookingEmailResult = await sendBookingConfirmationEmail(
          reservation.customer.email,
          customerName,
          {
            reservationNumber: reservation.reservation_number,
            serviceName,
            date,
            time,
            participants: reservation.participant_count,
            totalAmount: reservation.total_amount,
            specialRequests: reservation.special_requests,
          },
          pdfBuffer
        );

        results.push({
          type: "booking_confirmation",
          success: bookingEmailResult.success,
          messageId: bookingEmailResult.messageId,
          error: bookingEmailResult.error
        });

        // 2. Send payment confirmation email if payment exists
        if (reservation.payment && reservation.payment.length > 0) {
          const payment = reservation.payment[0]; // Get the first payment
          
          console.log("Sending payment confirmation email...");
          const paymentEmailResult = await sendPaymentConfirmationEmail(
            reservation.customer.email,
            customerName,
            {
              reservationNumber: reservation.reservation_number,
              serviceName,
              date,
              time,
              participants: reservation.participant_count,
              amount: payment.amount,
              currency: payment.currency.toUpperCase(),
              paymentDate: new Date(payment.payment_date || payment.created_at).toLocaleDateString("fr-FR"),
              isDeposit: payment.is_deposit,
              remainingAmount: payment.is_deposit ? reservation.remaining_amount : undefined,
            }
          );

          results.push({
            type: "payment_confirmation",
            success: paymentEmailResult.success,
            messageId: paymentEmailResult.messageId,
            error: paymentEmailResult.error
          });

          // 3. Send invoice email
          console.log("Generating and sending invoice...");
          try {
            const invoiceData = createInvoiceData(
              reservation,
              payment,
              reservation.customer,
              reservation.service
            );

            const invoicePDF = await generateInvoicePDF(invoiceData);
            const invoiceBuffer = Buffer.from(await invoicePDF.arrayBuffer());

            const invoiceEmailResult = await sendInvoiceEmail(
              reservation.customer.email,
              customerName,
              {
                invoiceNumber: invoiceData.invoiceNumber,
                reservationNumber: reservation.reservation_number,
                serviceName,
                totalAmount: payment.amount,
                currency: payment.currency.toUpperCase(),
                paymentStatus: payment.is_deposit ? "partial" : "paid",
              },
              invoiceBuffer
            );

            results.push({
              type: "invoice",
              success: invoiceEmailResult.success,
              messageId: invoiceEmailResult.messageId,
              error: invoiceEmailResult.error
            });
          } catch (invoiceError) {
            console.error("Invoice generation error:", invoiceError);
            results.push({
              type: "invoice",
              success: false,
              error: invoiceError instanceof Error ? invoiceError.message : "Invoice generation failed"
            });
          }
        }

        return NextResponse.json({
          success: true,
          message: "Emails resent",
          reservationNumber,
          reservation: {
            id: reservation.id,
            status: reservation.status,
            customer_email: reservation.customer.email,
            service_name: serviceName,
            date,
            time,
            participants: reservation.participant_count,
            total_amount: reservation.total_amount
          },
          emailResults: results
        });

      } catch (error) {
        console.error("Error resending emails:", error);
        return NextResponse.json({
          success: false,
          error: "Failed to resend emails",
          details: error instanceof Error ? error.message : "Unknown error",
          reservationNumber
        }, { status: 500 });
      }
    } else {
      // Just return reservation info for checking
      return NextResponse.json({
        success: true,
        message: "Reservation found",
        reservationNumber,
        reservation: {
          id: reservation.id,
          status: reservation.status,
          customer_email: reservation.customer.email,
          service_name: serviceName,
          date,
          time,
          participants: reservation.participant_count,
          total_amount: reservation.total_amount,
          payment_status: reservation.payment?.length > 0 ? reservation.payment[0].status : "no_payment",
          created_at: reservation.created_at,
          confirmed_at: reservation.confirmed_at
        }
      });
    }

  } catch (error) {
    console.error("Reservation email debug error:", error);
    return NextResponse.json({
      success: false,
      error: "Internal server error",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}
