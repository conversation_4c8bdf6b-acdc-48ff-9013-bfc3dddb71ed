import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export async function GET(request: NextRequest) {
	try {
		console.log("Debugging admin notification email setting...");

		// Check for the setting
		const { data: settings, error } = await supabase
			.from("business_settings")
			.select("*")
			.eq("key", "admin_notification_email");

		console.log("Query result:", { settings, error });

		return NextResponse.json({
			success: true,
			settings: settings || [],
			count: settings?.length || 0,
			error: error?.message || null,
		});
	} catch (error) {
		console.error("Error debugging admin setting:", error);
		return NextResponse.json(
			{
				success: false,
				error: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}
