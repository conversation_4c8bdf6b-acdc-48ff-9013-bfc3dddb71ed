import { NextRequest, NextResponse } from "next/server";
import { 
	sendAdminNewReservationNotification, 
	sendAdminPaymentReceivedNotification 
} from "@/lib/email-service";

export async function GET(request: NextRequest) {
	try {
		console.log("Testing admin notification system...");

		// Test data
		const reservationData = {
			reservationNumber: "TEST-ADMIN-001",
			customerName: "<PERSON> V<PERSON>nin",
			customerEmail: "<EMAIL>",
			serviceName: "Location de bateau - 70cv (avec permis)",
			date: "lundi 28 juillet 2025",
			time: "09:00",
			participants: 7,
			totalAmount: 380,
			specialRequests: "Test admin notification system"
		};

		const paymentData = {
			reservationNumber: "TEST-ADMIN-001",
			customerName: "<PERSON> V<PERSON>nin",
			amount: 380,
			currency: "EUR",
			paymentDate: new Date().toLocaleDateString("fr-FR"),
			isDeposit: false
		};

		console.log("Sending admin new reservation notification...");
		const reservationResult = await sendAdminNewReservationNotification(reservationData);

		console.log("Sending admin payment received notification...");
		const paymentResult = await sendAdminPaymentReceivedNotification(paymentData);

		return NextResponse.json({
			success: true,
			message: "Admin notification tests completed",
			results: {
				reservation: reservationResult,
				payment: paymentResult
			}
		});

	} catch (error) {
		console.error("Error testing admin notifications:", error);
		return NextResponse.json({
			success: false,
			error: error instanceof Error ? error.message : "Unknown error"
		}, { status: 500 });
	}
}
