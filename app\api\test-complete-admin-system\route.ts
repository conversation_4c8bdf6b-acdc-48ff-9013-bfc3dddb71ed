import { NextRequest, NextResponse } from "next/server";
import { getAdminNotificationEmail } from "@/lib/admin-notifications";
import { createClient } from "@supabase/supabase-js";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export async function GET(request: NextRequest) {
	try {
		console.log("=== TESTING COMPLETE ADMIN NOTIFICATION SYSTEM ===");

		// 1. Test getting admin notification email from settings
		console.log("1. Testing getAdminNotificationEmail function...");
		const adminEmail = await getAdminNotificationEmail();
		console.log("Admin email retrieved:", adminEmail);

		// 2. Verify the setting exists in database
		console.log("2. Verifying setting exists in database...");
		const { data: setting, error } = await supabase
			.from("business_settings")
			.select("*")
			.eq("key", "admin_notification_email")
			.single();

		if (error) {
			throw new Error(`Setting not found in database: ${error.message}`);
		}

		console.log("Setting found in database:", setting);

		// 3. Test updating the setting
		console.log("3. Testing setting update...");
		const testEmail = "<EMAIL>";

		const { data: updatedSetting, error: updateError } = await supabase
			.from("business_settings")
			.update({ value: testEmail })
			.eq("key", "admin_notification_email")
			.select()
			.single();

		if (updateError) {
			throw new Error(`Failed to update setting: ${updateError.message}`);
		}

		console.log("Setting updated successfully:", updatedSetting);

		// 4. Test retrieving updated email
		console.log("4. Testing retrieval of updated email...");
		const updatedAdminEmail = await getAdminNotificationEmail();
		console.log("Updated admin email retrieved:", updatedAdminEmail);

		// 5. Restore original setting
		console.log("5. Restoring original setting...");
		const { data: restoredSetting, error: restoreError } = await supabase
			.from("business_settings")
			.update({ value: "<EMAIL>" })
			.eq("key", "admin_notification_email")
			.select()
			.single();

		if (restoreError) {
			throw new Error(`Failed to restore setting: ${restoreError.message}`);
		}

		console.log("Setting restored successfully:", restoredSetting);

		// 6. Final verification
		console.log("6. Final verification...");
		const finalAdminEmail = await getAdminNotificationEmail();
		console.log("Final admin email:", finalAdminEmail);

		return NextResponse.json({
			success: true,
			message: "Complete admin notification system test passed",
			results: {
				originalEmail: adminEmail,
				settingInDatabase: setting,
				updatedEmail: updatedAdminEmail,
				finalEmail: finalAdminEmail,
				testPassed: finalAdminEmail === "<EMAIL>",
			},
		});
	} catch (error) {
		console.error("Error testing complete admin system:", error);
		return NextResponse.json(
			{
				success: false,
				error: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}
