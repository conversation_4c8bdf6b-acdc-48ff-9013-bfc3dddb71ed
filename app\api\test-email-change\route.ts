import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { getAdminNotificationEmail } from "@/lib/admin-notifications";
import { sendAdminNewReservationNotification } from "@/lib/email-service";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export async function GET(request: NextRequest) {
	try {
		console.log("=== TESTING EMAIL CHANGE FUNCTIONALITY ===");

		// 1. Get current admin email
		const originalEmail = await getAdminNotificationEmail();
		console.log("1. Original admin email:", originalEmail);

		// 2. Change to test email
		const testEmail = "<EMAIL>";
		console.log("2. Changing admin email to:", testEmail);
		
		const { error: updateError } = await supabase
			.from("business_settings")
			.update({ value: testEmail })
			.eq("key", "admin_notification_email");

		if (updateError) {
			throw new Error(`Failed to update email: ${updateError.message}`);
		}

		// 3. Verify the change
		const updatedEmail = await getAdminNotificationEmail();
		console.log("3. Updated admin email:", updatedEmail);

		// 4. Send test notification to new email
		console.log("4. Sending test notification to new email...");
		const notificationResult = await sendAdminNewReservationNotification({
			reservationNumber: "TEST-EMAIL-CHANGE-001",
			customerName: "Test Customer",
			customerEmail: "<EMAIL>",
			serviceName: "Test Service",
			date: "aujourd'hui",
			time: "10:00",
			participants: 2,
			totalAmount: 100,
			specialRequests: "Test email change functionality"
		});

		// 5. Restore original email
		console.log("5. Restoring original email...");
		const { error: restoreError } = await supabase
			.from("business_settings")
			.update({ value: originalEmail })
			.eq("key", "admin_notification_email");

		if (restoreError) {
			throw new Error(`Failed to restore email: ${restoreError.message}`);
		}

		// 6. Verify restoration
		const restoredEmail = await getAdminNotificationEmail();
		console.log("6. Restored admin email:", restoredEmail);

		return NextResponse.json({
			success: true,
			message: "Email change test completed successfully",
			results: {
				originalEmail,
				testEmail,
				updatedEmail,
				notificationSent: notificationResult.success,
				notificationMessageId: notificationResult.messageId,
				restoredEmail,
				testPassed: updatedEmail === testEmail && restoredEmail === originalEmail
			}
		});

	} catch (error) {
		console.error("Error testing email change:", error);
		return NextResponse.json({
			success: false,
			error: error instanceof Error ? error.message : "Unknown error"
		}, { status: 500 });
	}
}
